'use client';

import React from 'react';
import { CacheAware<PERSON>oader, SimpleCacheAwareLoader, useCacheAwareData, SmoothTransition } from './CacheAwareLoader';
import { SkeletonCard } from './SkeletonLoader';

/**
 * Example implementations of cache-aware loading patterns
 * 
 * ✅ Best Practice: Only show skeleton if real data is still being fetched
 * ✅ Skip skeleton for cached data and render instantly
 * ✅ Use smooth transitions for cached content
 */

// Example 1: Using CacheAwareLoader component
export function ExampleWithCacheAwareLoader() {
  return (
    <CacheAwareLoader
      cacheKey="user_profile"
      fetchFn={async () => {
        // Simulate API call
        const response = await fetch('/api/user/profile');
        return response.json();
      }}
      loadingComponent={
        <div className="space-y-4">
          <SkeletonCard withHeader={true} contentLines={3} />
          <SkeletonCard withHeader={true} contentLines={2} />
        </div>
      }
      ttl={5 * 60 * 1000} // 5 minutes
    >
      {(data, refresh) => (
        <div className="space-y-4">
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-bold">{data.name}</h2>
            <p className="text-gray-600">{data.email}</p>
          </div>
          <button 
            onClick={() => refresh(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded"
          >
            Refresh Data
          </button>
        </div>
      )}
    </CacheAwareLoader>
  );
}

// Example 2: Using SimpleCacheAwareLoader for basic cases
export function ExampleWithSimpleCacheAwareLoader() {
  return (
    <SimpleCacheAwareLoader
      cacheKey="user_settings"
      fetchFn={async () => {
        const response = await fetch('/api/user/settings');
        return response.json();
      }}
      skeleton={
        <SkeletonCard withHeader={true} contentLines={4} />
      }
      ttl={10 * 60 * 1000} // 10 minutes
    >
      {(settings) => (
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-4">Settings</h3>
          <div className="space-y-2">
            <div>Notifications: {settings.notifications ? 'On' : 'Off'}</div>
            <div>Theme: {settings.theme}</div>
            <div>Language: {settings.language}</div>
          </div>
        </div>
      )}
    </SimpleCacheAwareLoader>
  );
}

// Example 3: Using useCacheAwareData hook for manual control
export function ExampleWithManualControl() {
  const {
    data,
    isLoading,
    error,
    isFromCache,
    shouldShowSkeleton,
    refresh
  } = useCacheAwareData(
    'dashboard_stats',
    async () => {
      const response = await fetch('/api/dashboard/stats');
      if (!response.ok) throw new Error('Failed to fetch stats');
      return response.json();
    },
    {
      ttl: 2 * 60 * 1000, // 2 minutes
      onError: (error) => console.error('Stats error:', error),
      onSuccess: (data, fromCache) => {
        console.log(`Stats loaded ${fromCache ? 'from cache' : 'from API'}`);
      }
    }
  );

  // ✅ Only show skeleton when actually fetching (not for cached data)
  if (shouldShowSkeleton) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {[1, 2, 3].map((i) => (
          <SkeletonCard key={i} withHeader={true} contentLines={2} />
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 mb-4">Error: {error.message}</p>
        <button 
          onClick={() => refresh(true)}
          className="px-4 py-2 bg-red-600 text-white rounded"
        >
          Retry
        </button>
      </div>
    );
  }

  if (!data) {
    return <div className="text-center py-8">No data available</div>;
  }

  // ✅ Smooth transition for content (no transition for cached data)
  return (
    <SmoothTransition isFromCache={isFromCache}>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold">Total Users</h3>
          <p className="text-3xl font-bold text-blue-600">{data.totalUsers}</p>
          {isFromCache && <span className="text-xs text-gray-500">Cached</span>}
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold">Active Sessions</h3>
          <p className="text-3xl font-bold text-green-600">{data.activeSessions}</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold">Revenue</h3>
          <p className="text-3xl font-bold text-purple-600">${data.revenue}</p>
        </div>
      </div>
    </SmoothTransition>
  );
}

// Example 4: Page-level implementation
export function ExamplePageImplementation() {
  const {
    data: pageData,
    shouldShowSkeleton,
    isFromCache,
    refresh
  } = useCacheAwareData(
    'example_page_data',
    async () => {
      // Simulate fetching page data
      await new Promise(resolve => setTimeout(resolve, 1000));
      return {
        title: 'Example Page',
        content: 'This is example content that would be fetched from an API.',
        lastUpdated: new Date().toISOString()
      };
    },
    { ttl: 5 * 60 * 1000 }
  );

  // ✅ Only show skeleton when actually fetching
  if (shouldShowSkeleton) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="space-y-6">
          <SkeletonCard withHeader={true} contentLines={1} />
          <SkeletonCard withHeader={false} contentLines={5} />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <SkeletonCard withHeader={true} contentLines={3} />
            <SkeletonCard withHeader={true} contentLines={3} />
          </div>
        </div>
      </div>
    );
  }

  if (!pageData) {
    return (
      <div className="max-w-4xl mx-auto p-6 text-center">
        <p className="text-gray-500">Loading page...</p>
      </div>
    );
  }

  // ✅ Smooth transition for content
  return (
    <SmoothTransition isFromCache={isFromCache} className="max-w-4xl mx-auto p-6">
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">{pageData.title}</h1>
          <button 
            onClick={() => refresh(true)}
            className="px-4 py-2 bg-gray-600 text-white rounded text-sm"
          >
            Refresh
          </button>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <p className="text-gray-700 leading-relaxed">{pageData.content}</p>
          <div className="mt-4 pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-500">
              Last updated: {new Date(pageData.lastUpdated).toLocaleString()}
              {isFromCache && ' (from cache)'}
            </p>
          </div>
        </div>
      </div>
    </SmoothTransition>
  );
}

/**
 * Migration Guide Component
 * Shows before/after examples of skeleton loading patterns
 */
export function MigrationGuideExample() {
  return (
    <div className="space-y-8">
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-red-800 mb-4">❌ Bad: Artificial Delays</h3>
        <pre className="text-sm text-red-700 bg-red-100 p-4 rounded overflow-x-auto">
{`// ❌ Shows skeleton for 700ms even when data is cached
useEffect(() => {
  let timer: NodeJS.Timeout | null = null;
  if (!loading && userData) {
    timer = setTimeout(() => {
      setShowSkeleton(false);
    }, 700); // Artificial delay!
  }
  return () => timer && clearTimeout(timer);
}, [loading, userData]);`}
        </pre>
      </div>

      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-green-800 mb-4">✅ Good: Cache-Aware Loading</h3>
        <pre className="text-sm text-green-700 bg-green-100 p-4 rounded overflow-x-auto">
{`// ✅ Only shows skeleton when actually fetching data
const { data, shouldShowSkeleton, isFromCache } = useCacheAwareData(
  'cache_key',
  fetchFunction,
  { ttl: 5 * 60 * 1000 }
);

if (shouldShowSkeleton) {
  return <Skeleton />; // Only when fetching
}

return (
  <SmoothTransition isFromCache={isFromCache}>
    {/* Content renders immediately for cached data */}
  </SmoothTransition>
);`}
        </pre>
      </div>
    </div>
  );
}
