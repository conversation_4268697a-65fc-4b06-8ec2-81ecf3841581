'use client';

import React, { ReactNode } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useCacheAwareLoading, useShouldShowSkeleton } from '@/hooks/useCacheAwareLoading';

interface CacheAwareLoaderProps<T> {
  cacheKey: string;
  fetchFn: () => Promise<T>;
  children: (data: T, refresh: (forceRefresh?: boolean) => Promise<void>) => ReactNode;
  loadingComponent: ReactNode;
  errorComponent?: (error: Error, retry: () => Promise<void>) => ReactNode;
  ttl?: number;
  enableCache?: boolean;
  className?: string;
  onError?: (error: Error) => void;
  onSuccess?: (data: T, fromCache: boolean) => void;
}

/**
 * Cache-aware loader component that follows best practices:
 * ✅ Only shows skeleton when actually fetching data
 * ✅ Renders cached data immediately with smooth transitions
 * ✅ No artificial delays
 */
export function CacheAwareLoader<T>({
  cacheKey,
  fetchFn,
  children,
  loadingComponent,
  errorComponent,
  ttl,
  enableCache = true,
  className = '',
  onError,
  onSuccess
}: CacheAwareLoaderProps<T>) {
  const {
    data,
    isLoading,
    error,
    isFromCache,
    refresh
  } = useCacheAwareLoading({
    cacheKey,
    fetchFn,
    ttl,
    enableCache,
    onError,
    onSuccess
  });

  const shouldShowSkeleton = useShouldShowSkeleton(isLoading, !!data, isFromCache);

  // Handle error state
  if (error && !data) {
    return (
      <div className={className}>
        {errorComponent ? (
          errorComponent(error, () => refresh(true))
        ) : (
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">Error loading data: {error.message}</p>
            <button
              onClick={() => refresh(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Retry
            </button>
          </div>
        )}
      </div>
    );
  }

  // Show skeleton only when actually fetching (not for cached data)
  if (shouldShowSkeleton) {
    return <div className={className}>{loadingComponent}</div>;
  }

  // Render data with smooth transition
  if (data) {
    return (
      <AnimatePresence mode="wait">
        <motion.div
          key={isFromCache ? 'cached' : 'fresh'}
          initial={isFromCache ? { opacity: 1 } : { opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{
            duration: isFromCache ? 0 : 0.3, // No transition for cached data
            ease: 'easeOut'
          }}
          className={className}
        >
          {children(data, refresh)}
        </motion.div>
      </AnimatePresence>
    );
  }

  // Fallback (shouldn't normally reach here)
  return <div className={className}>{loadingComponent}</div>;
}

/**
 * Simplified wrapper for common use cases
 */
interface SimpleCacheAwareLoaderProps<T> {
  cacheKey: string;
  fetchFn: () => Promise<T>;
  children: (data: T) => ReactNode;
  skeleton: ReactNode;
  className?: string;
  ttl?: number;
}

export function SimpleCacheAwareLoader<T>({
  cacheKey,
  fetchFn,
  children,
  skeleton,
  className,
  ttl
}: SimpleCacheAwareLoaderProps<T>) {
  return (
    <CacheAwareLoader
      cacheKey={cacheKey}
      fetchFn={fetchFn}
      loadingComponent={skeleton}
      className={className}
      ttl={ttl}
    >
      {(data) => children(data)}
    </CacheAwareLoader>
  );
}

/**
 * Hook for manual cache-aware loading control
 * Use this when you need more control over the loading logic
 */
export function useCacheAwareData<T>(
  cacheKey: string,
  fetchFn: () => Promise<T>,
  options?: {
    ttl?: number;
    enableCache?: boolean;
    onError?: (error: Error) => void;
    onSuccess?: (data: T, fromCache: boolean) => void;
  }
) {
  const result = useCacheAwareLoading({
    cacheKey,
    fetchFn,
    ...options
  });

  const shouldShowSkeleton = useShouldShowSkeleton(
    result.isLoading,
    !!result.data,
    result.isFromCache
  );

  return {
    ...result,
    shouldShowSkeleton
  };
}

/**
 * Utility component for smooth content transitions
 */
interface SmoothTransitionProps {
  children: ReactNode;
  isFromCache: boolean;
  className?: string;
}

export function SmoothTransition({ children, isFromCache, className }: SmoothTransitionProps) {
  return (
    <motion.div
      initial={isFromCache ? { opacity: 1 } : { opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: isFromCache ? 0 : 0.4,
        ease: 'easeOut'
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}
