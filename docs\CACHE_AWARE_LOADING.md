# Cache-Aware Loading System

This document describes the cache-aware loading system that follows best practices for skeleton loading and smooth user experiences.

## ✅ Best Practice

**Only show the skeleton if real data is still being fetched.**

- If the data is already cached (like with React Query or SWR), skip the skeleton and render instantly
- If you want a smooth visual transition, use fade-ins or transitions on content instead of showing a loading skeleton

## 🚫 What We Fixed

### Before (Bad Practice)
```typescript
// ❌ Shows skeleton for 700ms even when data is cached
useEffect(() => {
  let skeletonTimer: NodeJS.Timeout | null = null;
  if (!initialLoading && userData) {
    // Add minimum 700ms delay for proper skeleton visibility
    skeletonTimer = setTimeout(() => {
      setShowSkeleton(false);
    }, 700); // Artificial delay!
  }
  return () => {
    if (skeletonTimer) {
      clearTimeout(skeletonTimer);
    }
  };
}, [initialLoading, userData]);
```

### After (Best Practice)
```typescript
// ✅ Only shows skeleton when actually fetching data
const {
  data,
  shouldShowSkeleton,
  isFromCache,
  refresh
} = useCacheAwareData(
  'cache_key',
  fetchFunction,
  { ttl: 5 * 60 * 1000 }
);

if (shouldShowSkeleton) {
  return <Skeleton />; // Only when fetching
}

return (
  <SmoothTransition isFromCache={isFromCache}>
    {/* Content renders immediately for cached data */}
  </SmoothTransition>
);
```

## 🛠️ API Reference

### `useCacheAwareLoading` Hook

Core hook that manages cache checking and loading states.

```typescript
const result = useCacheAwareLoading({
  cacheKey: string,
  fetchFn: () => Promise<T>,
  ttl?: number, // Time to live in milliseconds (default: 5 minutes)
  enableCache?: boolean, // Allow disabling cache for testing
  onError?: (error: Error) => void,
  onSuccess?: (data: T, fromCache: boolean) => void
});
```

**Returns:**
- `data: T | null` - The fetched/cached data
- `isLoading: boolean` - True only when actively fetching (not for cached data)
- `error: Error | null` - Any error that occurred
- `isFromCache: boolean` - Whether the data came from cache
- `refresh: (forceRefresh?: boolean) => Promise<void>` - Function to refresh data
- `clearCache: () => void` - Function to clear cached data

### `useCacheAwareData` Hook

Simplified hook with built-in skeleton logic.

```typescript
const {
  data,
  isLoading,
  error,
  isFromCache,
  shouldShowSkeleton, // ✅ Only true when actually fetching
  refresh,
  clearCache
} = useCacheAwareData(cacheKey, fetchFn, options);
```

### `CacheAwareLoader` Component

Wrapper component for declarative cache-aware loading.

```typescript
<CacheAwareLoader
  cacheKey="user_profile"
  fetchFn={async () => fetch('/api/profile').then(r => r.json())}
  loadingComponent={<Skeleton />}
  ttl={5 * 60 * 1000}
>
  {(data, refresh) => (
    <div>
      <h1>{data.name}</h1>
      <button onClick={() => refresh(true)}>Refresh</button>
    </div>
  )}
</CacheAwareLoader>
```

### `SmoothTransition` Component

Provides smooth animations for content transitions.

```typescript
<SmoothTransition isFromCache={isFromCache} className="container">
  {/* Your content */}
</SmoothTransition>
```

**Animation Behavior:**
- **Cached data**: No animation (renders immediately)
- **Fresh data**: Smooth fade-in with slight upward motion

## 📋 Migration Guide

### Step 1: Replace Artificial Delays

**Before:**
```typescript
const [showSkeleton, setShowSkeleton] = useState(true);
const [initialLoading, setInitialLoading] = useState(true);

useEffect(() => {
  let skeletonTimer: NodeJS.Timeout | null = null;
  if (!initialLoading && userData) {
    skeletonTimer = setTimeout(() => {
      setShowSkeleton(false);
    }, 700); // Remove this!
  }
  return () => {
    if (skeletonTimer) {
      clearTimeout(skeletonTimer);
    }
  };
}, [initialLoading, userData]);
```

**After:**
```typescript
const {
  data: userData,
  shouldShowSkeleton,
  isFromCache
} = useCacheAwareData('user_data', fetchUserData);
```

### Step 2: Update Loading Conditions

**Before:**
```typescript
if (!userData || showSkeleton || initialLoading) {
  return <Skeleton />;
}
```

**After:**
```typescript
if (shouldShowSkeleton) {
  return <Skeleton />;
}

if (!userData) {
  return <div>Loading...</div>;
}
```

### Step 3: Add Smooth Transitions

**Before:**
```typescript
return (
  <div className="content">
    {/* Your content */}
  </div>
);
```

**After:**
```typescript
return (
  <SmoothTransition isFromCache={isFromCache}>
    <div className="content">
      {/* Your content */}
    </div>
  </SmoothTransition>
);
```

## 🎯 Benefits

1. **Faster Perceived Performance**: Cached data renders immediately
2. **Better User Experience**: No unnecessary loading animations
3. **Consistent Behavior**: Predictable loading states across the app
4. **Reduced Flicker**: Smooth transitions instead of abrupt changes
5. **Cache Management**: Built-in TTL and cache invalidation

## 🔧 Configuration

### Cache TTL (Time To Live)
```typescript
// Short-lived data (2 minutes)
useCacheAwareData('live_stats', fetchStats, { ttl: 2 * 60 * 1000 });

// Medium-lived data (5 minutes) - default
useCacheAwareData('user_profile', fetchProfile);

// Long-lived data (30 minutes)
useCacheAwareData('app_config', fetchConfig, { ttl: 30 * 60 * 1000 });
```

### Disable Cache (for testing)
```typescript
useCacheAwareData('test_data', fetchData, { enableCache: false });
```

### Error Handling
```typescript
useCacheAwareData('api_data', fetchData, {
  onError: (error) => {
    console.error('Failed to fetch data:', error);
    showToast(error.message, 'error');
  },
  onSuccess: (data, fromCache) => {
    console.log(`Data loaded ${fromCache ? 'from cache' : 'from API'}`);
  }
});
```

## 📝 Examples

See `src/components/ui/CacheAwareExamples.tsx` for complete working examples of:
- Basic cache-aware loading
- Manual control with hooks
- Page-level implementation
- Error handling patterns
- Migration examples
