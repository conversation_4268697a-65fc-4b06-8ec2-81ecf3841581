'use client';

import { useState, useEffect, useCallback, useRef } from 'react';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

interface CacheAwareLoadingOptions<T> {
  cacheKey: string;
  fetchFn: () => Promise<T>;
  ttl?: number; // Time to live in milliseconds (default: 5 minutes)
  enableCache?: boolean; // Allow disabling cache for testing
  onError?: (error: Error) => void;
  onSuccess?: (data: T, fromCache: boolean) => void;
}

interface CacheAwareLoadingResult<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
  isFromCache: boolean;
  refresh: (forceRefresh?: boolean) => Promise<void>;
  clearCache: () => void;
}

/**
 * Cache-aware loading hook that only shows loading states when actually fetching data.
 * If data is cached and fresh, it returns immediately without showing loading state.
 * 
 * ✅ Best Practice: Only show skeleton if real data is still being fetched
 * ✅ Skip skeleton for cached data and render instantly
 * ✅ Use smooth transitions for cached content
 */
export function useCacheAwareLoading<T>({
  cacheKey,
  fetchFn,
  ttl = 5 * 60 * 1000, // 5 minutes default
  enableCache = true,
  onError,
  onSuccess
}: CacheAwareLoadingOptions<T>): CacheAwareLoadingResult<T> {
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isFromCache, setIsFromCache] = useState(false);
  
  const isMountedRef = useRef(true);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Cache utilities
  const getCachedData = useCallback((): T | null => {
    if (!enableCache || typeof window === 'undefined') return null;
    
    try {
      const cached = sessionStorage.getItem(`cache_${cacheKey}`);
      if (!cached) return null;
      
      const entry: CacheEntry<T> = JSON.parse(cached);
      const now = Date.now();
      
      // Check if cache is still valid
      if (now - entry.timestamp > entry.ttl) {
        sessionStorage.removeItem(`cache_${cacheKey}`);
        return null;
      }
      
      return entry.data;
    } catch (error) {
      console.warn('Failed to read cache:', error);
      return null;
    }
  }, [cacheKey, enableCache, ttl]);

  const setCachedData = useCallback((data: T): void => {
    if (!enableCache || typeof window === 'undefined') return;
    
    try {
      const entry: CacheEntry<T> = {
        data,
        timestamp: Date.now(),
        ttl
      };
      sessionStorage.setItem(`cache_${cacheKey}`, JSON.stringify(entry));
    } catch (error) {
      console.warn('Failed to cache data:', error);
    }
  }, [cacheKey, enableCache, ttl]);

  const clearCache = useCallback((): void => {
    if (typeof window === 'undefined') return;
    
    try {
      sessionStorage.removeItem(`cache_${cacheKey}`);
    } catch (error) {
      console.warn('Failed to clear cache:', error);
    }
  }, [cacheKey]);

  // Main fetch function
  const fetchData = useCallback(async (forceRefresh = false): Promise<void> => {
    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Check cache first (unless forcing refresh)
    if (!forceRefresh) {
      const cachedData = getCachedData();
      if (cachedData) {
        setData(cachedData);
        setIsFromCache(true);
        setError(null);
        onSuccess?.(cachedData, true);
        return; // ✅ Return immediately for cached data - no loading state!
      }
    }

    // Only set loading state when actually fetching fresh data
    setIsLoading(true);
    setIsFromCache(false);
    setError(null);

    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    try {
      const result = await fetchFn();
      
      if (!isMountedRef.current) return;
      
      setData(result);
      setCachedData(result);
      onSuccess?.(result, false);
    } catch (err) {
      if (!isMountedRef.current) return;
      
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
      onError?.(error);
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false);
      }
      abortControllerRef.current = null;
    }
  }, [fetchFn, getCachedData, setCachedData, onError, onSuccess]);

  // Refresh function that can optionally bypass cache
  const refresh = useCallback(async (forceRefresh = false): Promise<void> => {
    await fetchData(forceRefresh);
  }, [fetchData]);

  // Initial load
  useEffect(() => {
    fetchData();
    
    return () => {
      isMountedRef.current = false;
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fetchData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return {
    data,
    isLoading,
    error,
    isFromCache,
    refresh,
    clearCache
  };
}

/**
 * Utility hook for checking if data should show loading skeleton
 * ✅ Only returns true when actually fetching (not when using cache)
 */
export function useShouldShowSkeleton(isLoading: boolean, hasData: boolean, isFromCache: boolean): boolean {
  // Only show skeleton when:
  // 1. Actually loading (fetching fresh data)
  // 2. No data available yet
  // 3. Data is not from cache (cache should render immediately)
  return isLoading && !hasData && !isFromCache;
}
